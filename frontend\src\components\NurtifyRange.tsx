import { ChangeEvent } from 'react';
import "./styles.css";

interface NurtifyRangeProps {
    min?: number;
    max?: number;
    step?: number;
    value?: number;
    onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
    className?: string;
    required?: boolean;
}

const NurtifyRange: React.FC<NurtifyRangeProps> = ({
    min = 0,
    max = 100,
    step = 1,
    value,
    onChange,
    className
}) => {
    // Ensure value is always controlled (never undefined)
    const controlledValue = value ?? min;

    return (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '10px' }}>
            <input
                type="range"
                min={min}
                max={max}
                step={step}
                value={controlledValue}
                onChange={onChange}
                className={`nurtify-range ${className || ''}`}
            />
            <span style={{ marginTop: '0px' }}>{controlledValue}</span>
        </div>
    );
};

export default NurtifyRange; 