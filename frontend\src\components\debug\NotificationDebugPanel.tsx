import React, { useState, useEffect } from 'react';
import { notificationPerformanceMonitor, logNotificationStats } from '@/utils/notificationPerformance';

const NotificationDebugPanel: React.FC = () => {
  const [stats, setStats] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Update stats every 5 seconds
    const interval = setInterval(() => {
      const currentStats = notificationPerformanceMonitor.getStats();
      setStats(currentStats);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  if (!import.meta.env.DEV) {
    return null; // Only show in development
  }

  return (
    <div style={{ 
      position: 'fixed', 
      bottom: '20px', 
      right: '20px', 
      zIndex: 9999,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '8px',
      fontSize: '12px',
      fontFamily: 'monospace'
    }}>
      <button 
        onClick={() => setIsVisible(!isVisible)}
        style={{
          background: 'none',
          border: '1px solid white',
          color: 'white',
          padding: '4px 8px',
          borderRadius: '4px',
          cursor: 'pointer',
          marginBottom: isVisible ? '10px' : '0'
        }}
      >
        {isVisible ? 'Hide' : 'Show'} Notification Debug
      </button>
      
      {isVisible && (
        <div>
          <div style={{ marginBottom: '10px' }}>
            <strong>Notification Performance</strong>
          </div>
          
          {stats ? (
            <div>
              <div>Total: {stats.count}</div>
              <div>Avg Time: {stats.averageTime.toFixed(2)}ms</div>
              <div>Min: {stats.minTime.toFixed(2)}ms</div>
              <div>Max: {stats.maxTime.toFixed(2)}ms</div>
              <div>WebSocket: {stats.websocketCount}</div>
              <div>Polling: {stats.pollingCount}</div>
            </div>
          ) : (
            <div>No notifications processed yet</div>
          )}
          
          <div style={{ marginTop: '10px' }}>
            <button 
              onClick={logNotificationStats}
              style={{
                background: 'none',
                border: '1px solid white',
                color: 'white',
                padding: '2px 6px',
                borderRadius: '4px',
                cursor: 'pointer',
                marginRight: '5px',
                fontSize: '10px'
              }}
            >
              Log Stats
            </button>
            <button 
              onClick={() => {
                notificationPerformanceMonitor.clear();
                setStats(null);
              }}
              style={{
                background: 'none',
                border: '1px solid white',
                color: 'white',
                padding: '2px 6px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '10px'
              }}
            >
              Clear
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationDebugPanel;
