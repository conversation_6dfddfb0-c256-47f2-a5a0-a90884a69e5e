/**
 * Notification performance monitoring utilities
 */

interface NotificationTiming {
  id: string;
  receivedAt: number;
  processedAt?: number;
  displayedAt?: number;
  type: 'websocket' | 'polling';
}

class NotificationPerformanceMonitor {
  private timings: Map<string, NotificationTiming> = new Map();
  private isEnabled: boolean = true;

  constructor() {
    // Enable performance monitoring in development
    this.isEnabled = import.meta.env.DEV;
  }

  /**
   * Mark when a notification was received
   */
  markReceived(notificationId: string, type: 'websocket' | 'polling') {
    if (!this.isEnabled) return;
    
    this.timings.set(notificationId, {
      id: notificationId,
      receivedAt: performance.now(),
      type
    });
    
    console.log(`📨 Notification ${notificationId} received via ${type} at ${performance.now()}`);
  }

  /**
   * Mark when a notification was processed (state updated)
   */
  markProcessed(notificationId: string) {
    if (!this.isEnabled) return;
    
    const timing = this.timings.get(notificationId);
    if (timing) {
      timing.processedAt = performance.now();
      const processingTime = timing.processedAt - timing.receivedAt;
      console.log(`⚡ Notification ${notificationId} processed in ${processingTime.toFixed(2)}ms`);
    }
  }

  /**
   * Mark when a notification was displayed in UI
   */
  markDisplayed(notificationId: string) {
    if (!this.isEnabled) return;
    
    const timing = this.timings.get(notificationId);
    if (timing) {
      timing.displayedAt = performance.now();
      const totalTime = timing.displayedAt - timing.receivedAt;
      const processingTime = timing.processedAt ? timing.processedAt - timing.receivedAt : 0;
      const renderTime = timing.processedAt ? timing.displayedAt - timing.processedAt : totalTime;
      
      console.log(`🎯 Notification ${notificationId} displayed in ${totalTime.toFixed(2)}ms total (processing: ${processingTime.toFixed(2)}ms, render: ${renderTime.toFixed(2)}ms)`);
      
      // Clean up old timings to prevent memory leaks
      setTimeout(() => this.timings.delete(notificationId), 30000);
    }
  }

  /**
   * Get performance statistics
   */
  getStats() {
    if (!this.isEnabled) return null;
    
    const completedTimings = Array.from(this.timings.values()).filter(t => t.displayedAt);
    
    if (completedTimings.length === 0) return null;
    
    const totalTimes = completedTimings.map(t => t.displayedAt! - t.receivedAt);
    const avgTime = totalTimes.reduce((a, b) => a + b, 0) / totalTimes.length;
    const maxTime = Math.max(...totalTimes);
    const minTime = Math.min(...totalTimes);
    
    return {
      count: completedTimings.length,
      averageTime: avgTime,
      maxTime,
      minTime,
      websocketCount: completedTimings.filter(t => t.type === 'websocket').length,
      pollingCount: completedTimings.filter(t => t.type === 'polling').length
    };
  }

  /**
   * Log performance statistics
   */
  logStats() {
    const stats = this.getStats();
    if (stats) {
      console.group('📊 Notification Performance Stats');
      console.log(`Total notifications: ${stats.count}`);
      console.log(`Average time: ${stats.averageTime.toFixed(2)}ms`);
      console.log(`Min time: ${stats.minTime.toFixed(2)}ms`);
      console.log(`Max time: ${stats.maxTime.toFixed(2)}ms`);
      console.log(`WebSocket: ${stats.websocketCount}, Polling: ${stats.pollingCount}`);
      console.groupEnd();
    }
  }

  /**
   * Clear all timings
   */
  clear() {
    this.timings.clear();
  }
}

// Export singleton instance
export const notificationPerformanceMonitor = new NotificationPerformanceMonitor();

// Convenience functions
export const markNotificationReceived = (id: string, type: 'websocket' | 'polling') => 
  notificationPerformanceMonitor.markReceived(id, type);

export const markNotificationProcessed = (id: string) => 
  notificationPerformanceMonitor.markProcessed(id);

export const markNotificationDisplayed = (id: string) => 
  notificationPerformanceMonitor.markDisplayed(id);

export const logNotificationStats = () => 
  notificationPerformanceMonitor.logStats();
