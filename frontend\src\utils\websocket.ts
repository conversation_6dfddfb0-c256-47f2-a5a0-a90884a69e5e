/**
 * WebSocket utility functions for consistent WebSocket URL configuration
 */

/**
 * Get the WebSocket base URL from environment variables
 * @returns The WebSocket base URL
 */
export const getWebSocketBaseUrl = (): string => {
  return import.meta.env.VITE_SOCKET_BASE_URL || 'ws://localhost:8003';
};

/**
 * Create a WebSocket URL for notifications
 * @param token - Authentication token (optional)
 * @returns Complete WebSocket URL for notifications
 */
export const createNotificationWebSocketUrl = (token?: string): string => {
  const baseUrl = getWebSocketBaseUrl();
  const tokenParam = token ? `?token=${token}` : '';
  return `${baseUrl}/socket/notifications/${tokenParam}`;
};

/**
 * Create a WebSocket URL for live chat
 * @param chatId - Chat UUID
 * @returns Complete WebSocket URL for live chat
 */
export const createLiveChatWebSocketUrl = (chatId: string): string => {
  const baseUrl = getWebSocketBaseUrl();
  return `${baseUrl}/socket/live-chat/${chatId}/`;
};

/**
 * Create a WebSocket connection with standard error handling and heartbeat
 * @param url - WebSocket URL
 * @param onMessage - Message handler
 * @param onOpen - Open handler (optional)
 * @param onClose - Close handler (optional)
 * @param onError - Error handler (optional)
 * @param enableHeartbeat - Enable heartbeat/ping functionality (default: true)
 * @returns WebSocket instance
 */
export const createWebSocketConnection = (
  url: string,
  onMessage: (event: MessageEvent) => void,
  onOpen?: () => void,
  onClose?: (event: CloseEvent) => void,
  onError?: (error: Event) => void,
  enableHeartbeat: boolean = true
): WebSocket => {
  const ws = new WebSocket(url);
  let heartbeatInterval: NodeJS.Timeout | null = null;

  ws.onopen = () => {
    console.log(`WebSocket connected to: ${url}`);

    // Start heartbeat to keep connection alive
    if (enableHeartbeat) {
      heartbeatInterval = setInterval(() => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify({ type: 'ping' }));
        }
      }, 30000); // Send ping every 30 seconds
    }

    onOpen?.();
  };

  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      // Handle pong responses silently
      if (data.type === 'pong') {
        return;
      }

      // Process message immediately without any delays
      setTimeout(() => onMessage(event), 0); // Use setTimeout to ensure immediate processing
    } catch (error) {
      // If it's not JSON, pass the raw message immediately
      setTimeout(() => onMessage(event), 0);
    }
  };

  ws.onclose = (event) => {
    console.log(`WebSocket closed: ${url}`, event.code, event.reason);

    // Clear heartbeat interval
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }

    onClose?.(event);
  };

  ws.onerror = (error) => {
    console.error(`WebSocket error: ${url}`, error);

    // Clear heartbeat interval on error
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }

    onError?.(error);
  };

  return ws;
};

/**
 * Check if WebSocket is ready to send messages
 * @param ws - WebSocket instance
 * @returns True if WebSocket is open and ready
 */
export const isWebSocketReady = (ws: WebSocket | null): boolean => {
  return ws !== null && ws.readyState === WebSocket.OPEN;
};

/**
 * Send a message through WebSocket with error handling
 * @param ws - WebSocket instance
 * @param message - Message to send
 * @returns True if message was sent successfully
 */
export const sendWebSocketMessage = (ws: WebSocket | null, message: any): boolean => {
  if (!isWebSocketReady(ws)) {
    console.warn('Cannot send message: WebSocket not ready');
    return false;
  }

  try {
    ws!.send(JSON.stringify(message));
    return true;
  } catch (error) {
    console.error('Error sending WebSocket message:', error);
    return false;
  }
};
