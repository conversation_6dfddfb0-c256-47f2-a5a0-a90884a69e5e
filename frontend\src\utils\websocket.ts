/**
 * WebSocket utility functions for consistent WebSocket URL configuration
 */

/**
 * Get the WebSocket base URL from environment variables
 * @returns The WebSocket base URL
 */
export const getWebSocketBaseUrl = (): string => {
  return import.meta.env.VITE_SOCKET_BASE_URL || 'ws://localhost:8003';
};

/**
 * Create a WebSocket URL for notifications
 * @param token - Authentication token (optional)
 * @returns Complete WebSocket URL for notifications
 */
export const createNotificationWebSocketUrl = (token?: string): string => {
  const baseUrl = getWebSocketBaseUrl();
  const tokenParam = token ? `?token=${token}` : '';
  return `${baseUrl}/socket/notifications/${tokenParam}`;
};

/**
 * Create a WebSocket URL for live chat
 * @param chatId - Chat UUID
 * @returns Complete WebSocket URL for live chat
 */
export const createLiveChatWebSocketUrl = (chatId: string): string => {
  const baseUrl = getWebSocketBaseUrl();
  return `${baseUrl}/socket/live-chat/${chatId}/`;
};

/**
 * Create a WebSocket connection with standard error handling
 * @param url - WebSocket URL
 * @param onMessage - Message handler
 * @param onOpen - Open handler (optional)
 * @param onClose - Close handler (optional)
 * @param onError - Error handler (optional)
 * @returns WebSocket instance
 */
export const createWebSocketConnection = (
  url: string,
  onMessage: (event: MessageEvent) => void,
  onOpen?: () => void,
  onClose?: (event: CloseEvent) => void,
  onError?: (error: Event) => void
): WebSocket => {
  const ws = new WebSocket(url);

  ws.onopen = () => {
    console.log(`WebSocket connected to: ${url}`);
    onOpen?.();
  };

  ws.onmessage = onMessage;

  ws.onclose = (event) => {
    console.log(`WebSocket closed: ${url}`, event.code, event.reason);
    onClose?.(event);
  };

  ws.onerror = (error) => {
    console.error(`WebSocket error: ${url}`, error);
    onError?.(error);
  };

  return ws;
};

/**
 * Check if WebSocket is ready to send messages
 * @param ws - WebSocket instance
 * @returns True if WebSocket is open and ready
 */
export const isWebSocketReady = (ws: WebSocket | null): boolean => {
  return ws !== null && ws.readyState === WebSocket.OPEN;
};

/**
 * Send a message through WebSocket with error handling
 * @param ws - WebSocket instance
 * @param message - Message to send
 * @returns True if message was sent successfully
 */
export const sendWebSocketMessage = (ws: WebSocket | null, message: any): boolean => {
  if (!isWebSocketReady(ws)) {
    console.warn('Cannot send message: WebSocket not ready');
    return false;
  }

  try {
    ws!.send(JSON.stringify(message));
    return true;
  } catch (error) {
    console.error('Error sending WebSocket message:', error);
    return false;
  }
};
