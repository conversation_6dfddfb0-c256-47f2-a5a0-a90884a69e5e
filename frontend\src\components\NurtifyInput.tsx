import React, { ChangeEvent } from "react"; // Import React
import "./styles.css";

interface NurtifyInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
    label?: string;
    placeholder?: string;
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
    className?: string;
    disabled?: boolean;
    leftIcon?: React.ReactNode; // Add optional leftIcon prop
}

const NurtifyInput: React.FC<NurtifyInputProps> = ({
    label,
    name,
    value,
    onChange,
    onKeyDown,
    placeholder,
    className, // Keep original className prop name
    disabled,
    leftIcon,
    ...rest
}) => {

    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
        onChange?.(e);
    };

    // Ensure value is always controlled (never undefined)
    const controlledValue = value ?? '';

    return (
        // Use the original nurtify-input class and append the passed className
        <div className={`nurtify-input ${className || ""}`}>
            {label && <label htmlFor={name}>{label}</label>}
            {/* Add a relative wrapper for positioning the icon */}
            <div style={{ position: 'relative', display: 'flex', alignItems: 'center', width: '100%' }}>
                {leftIcon && (
                    // Position the icon absolutely inside the wrapper
                    <div style={{ position: 'absolute', left: '0px', top: '50%', transform: 'translateY(-50%)', display: 'flex', alignItems: 'center', paddingLeft: '5px', pointerEvents: 'none' }}>
                        {leftIcon}
                    </div>
                )}
                <input
                    // Apply conditional padding-left directly to the input
                    // Keep original styles from styles.css (border: none, background: transparent, etc.)
                    style={{
                        width: "100%",
                        paddingLeft: leftIcon ? '30px' : '0px', // Adjust padding based on icon presence
                        // Ensure other necessary inline styles are kept if any, otherwise rely on CSS file
                    }}
                    type={rest.type}
                    id={name}
                    max={rest.max}
                    min={rest.min}
                    name={name}
                    value={controlledValue}
                    onChange={handleChange}
                    onKeyDown={onKeyDown}
                    placeholder={placeholder || "Place your text here"}
                    disabled={disabled}
                    // No className needed here, base style is in styles.css
                    {...rest} // Spread the rest of the props
                />
            </div>
        </div>
    );
}

export default NurtifyInput;
