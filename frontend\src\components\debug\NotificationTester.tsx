import React, { useState } from 'react';
import { useKeycloak } from '@react-keycloak/web';
import api from '@/services/api';

const NotificationTester: React.FC = () => {
  const [testResult, setTestResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const { keycloak } = useKeycloak();

  const testNotificationAPI = async () => {
    setIsLoading(true);
    setTestResult('Testing notification APIs...\n');
    
    try {
      // Test live chat notifications
      console.log('Testing /live-chat/notifications/');
      const liveChatResponse = await api.get('/live-chat/notifications/');
      setTestResult(prev => prev + `Live Chat Notifications Response:\n${JSON.stringify(liveChatResponse.data, null, 2)}\n\n`);
      
      // Test system notifications
      console.log('Testing /notification/notifications/recent/');
      const systemResponse = await api.get('/notification/notifications/recent/');
      setTestResult(prev => prev + `System Notifications Response:\n${JSON.stringify(systemResponse.data, null, 2)}\n\n`);
      
      // Test unread counts
      console.log('Testing unread counts');
      const liveChatUnreadResponse = await api.get('/live-chat/notifications/unread_count/');
      const systemUnreadResponse = await api.get('/notification/notifications/unread_count/');
      
      setTestResult(prev => prev + `Live Chat Unread Count:\n${JSON.stringify(liveChatUnreadResponse.data, null, 2)}\n\n`);
      setTestResult(prev => prev + `System Unread Count:\n${JSON.stringify(systemUnreadResponse.data, null, 2)}\n\n`);
      
    } catch (error) {
      console.error('API test error:', error);
      setTestResult(prev => prev + `Error: ${error}\n`);
    } finally {
      setIsLoading(false);
    }
  };

  const testWebSocketConnection = () => {
    if (!keycloak.token) {
      setTestResult('No Keycloak token available for WebSocket test');
      return;
    }

    setTestResult('Testing WebSocket connection...\n');
    
    const socketBaseUrl = import.meta.env.VITE_SOCKET_BASE_URL || 'ws://localhost:8003';
    const socketUrl = `${socketBaseUrl}/socket/notifications/?token=${keycloak.token}`;
    
    const ws = new WebSocket(socketUrl);
    
    ws.onopen = () => {
      setTestResult(prev => prev + 'WebSocket connected successfully!\n');
      
      // Send a test message
      ws.send(JSON.stringify({ type: 'ping' }));
      setTestResult(prev => prev + 'Sent ping message\n');
    };
    
    ws.onmessage = (event) => {
      setTestResult(prev => prev + `Received message: ${event.data}\n`);
    };
    
    ws.onerror = (error) => {
      setTestResult(prev => prev + `WebSocket error: ${error}\n`);
    };
    
    ws.onclose = (event) => {
      setTestResult(prev => prev + `WebSocket closed: ${event.code} - ${event.reason}\n`);
    };
    
    // Close after 10 seconds
    setTimeout(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    }, 10000);
  };

  if (!import.meta.env.DEV) {
    return null; // Only show in development
  }

  return (
    <div style={{ 
      position: 'fixed', 
      top: '20px', 
      left: '20px', 
      zIndex: 9999,
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      color: 'white',
      padding: '15px',
      borderRadius: '8px',
      fontSize: '12px',
      fontFamily: 'monospace',
      maxWidth: '400px',
      maxHeight: '500px',
      overflow: 'auto'
    }}>
      <div style={{ marginBottom: '10px' }}>
        <strong>Notification System Tester</strong>
      </div>
      
      <div style={{ marginBottom: '10px' }}>
        <button 
          onClick={testNotificationAPI}
          disabled={isLoading}
          style={{
            background: '#37B7C3',
            border: 'none',
            color: 'white',
            padding: '6px 12px',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px',
            fontSize: '11px'
          }}
        >
          {isLoading ? 'Testing...' : 'Test APIs'}
        </button>
        
        <button 
          onClick={testWebSocketConnection}
          style={{
            background: '#10b981',
            border: 'none',
            color: 'white',
            padding: '6px 12px',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '11px'
          }}
        >
          Test WebSocket
        </button>
      </div>
      
      {testResult && (
        <div style={{
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          padding: '10px',
          borderRadius: '4px',
          whiteSpace: 'pre-wrap',
          fontSize: '10px',
          maxHeight: '300px',
          overflow: 'auto'
        }}>
          {testResult}
        </div>
      )}
    </div>
  );
};

export default NotificationTester;
