import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Phone, Send } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import api from '@/services/api';
import './LiveChat.css';
import { useSendLiveChatMessageMutation, useUpdateChatStatusMutation, useMarkChatMessagesAsReadMutation } from '@/hooks/livechat.query';
import { useQueryClient } from '@tanstack/react-query';
import type { LiveChatMessage, LiveChat, LiveChatUser } from '@/services/api/livechat.types'; // Import LiveChatUser
import { createLiveChatWebSocketUrl } from '@/utils/websocket';

// Removed local UserProfile type, will use LiveChatUser directly for sender

const OrgChatDetail: React.FC = () => {
  const { uuid } = useParams<{ uuid: string }>();
  const navigate = useNavigate();
  const [newMessage, setNewMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);

  const { data: chat } = useQuery({
    queryKey: ['org-chat', uuid],
    queryFn: async () => {
      const response = await api.get(`/live-chat/chats/${uuid}/`);
      return response.data as LiveChat;
    },
    enabled: !!uuid,
  });

  const { data: messages = [] } = useQuery({
    queryKey: ['org-chat-messages', uuid],
    queryFn: async () => {
      const response = await api.get(`/live-chat/chats/${uuid}/messages/`);
      return response.data as LiveChatMessage[];
    },
    enabled: !!uuid,
  });

  const sendMessageMutation = useSendLiveChatMessageMutation();
  const updateChatStatusMutation = useUpdateChatStatusMutation();
  const markChatMessagesAsRead = useMarkChatMessagesAsReadMutation();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (!uuid) return;

    // Construct WebSocket URL using the selected chat UUID
    const wsUrl = createLiveChatWebSocketUrl(uuid);
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      // console.log(`[OrgChatDetail] WebSocket connection OPENED for ${uuid}.`);
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        if (data.type === 'chat_message' && data.message && uuid && chat) {
          const rawWsMessage = data.message;
          let messageSender: LiveChatUser;
          const wsSender = rawWsMessage.sender;

          if (chat.patient && wsSender && wsSender.identifier === chat.patient.identifier) {
            messageSender = chat.patient;
          } else if (wsSender) {
            messageSender = {
              identifier: wsSender.identifier || `unknown-${rawWsMessage.uuid}`,
              first_name: wsSender.first_name || 'Unknown',
              last_name: wsSender.last_name || 'User',
              email: wsSender.email || '<EMAIL>',
            };
            if (wsSender.image) messageSender.image = wsSender.image;
          } else {
            messageSender = {
              identifier: `fallback-${rawWsMessage.uuid}`,
              first_name: 'Error', last_name: 'Sender', email: '<EMAIL>',
            };
          }

          const receivedMessageForCache: LiveChatMessage = {
            uuid: rawWsMessage.uuid,
            content: rawWsMessage.content,
            sender: messageSender,
            sender_role: wsSender?.role?.toUpperCase() === 'PATIENT' ? 'PATIENT' :
                         wsSender?.role?.toUpperCase() === 'STAFF' ? 'STAFF' :
                         wsSender?.role?.toUpperCase() === 'ADMIN' ? 'ADMIN' : null,
            created_at: rawWsMessage.created_at,
            is_system_message: rawWsMessage.is_system_message || false,
            is_read: rawWsMessage.is_read || false,
          };

           queryClient.setQueryData<LiveChatMessage[]>(['org-chat-messages', uuid], (oldMessages = []) => {
             if (oldMessages.some(msg => msg.uuid === receivedMessageForCache.uuid)) {
               return oldMessages;
             }
             return [...oldMessages, receivedMessageForCache].sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
           });
        }
      } catch (error) {
        console.error('[OrgChatDetail] Error parsing WebSocket message:', error);
      }
    };

    ws.onerror = () => {
      // console.error(`[OrgChatDetail] WebSocket ERROR for ${uuid}:`, error);
    };

    ws.onclose = (event) => {
      // console.log(`[OrgChatDetail] WebSocket connection CLOSED for ${uuid}. Code: ${event.code}, Reason: '${event.reason}', Clean: ${event.wasClean}`);
      if (event.code !== 1000 && event.code !== 1001 && uuid) {
        // setTimeout(() => {
        // }, 5000);
      }
    };

    return () => {
      if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
        ws.close(1000, "Component unmounting or dependencies changed");
      }
    };
  }, [uuid, queryClient, chat]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newMessage.trim() && !sendMessageMutation.isPending && uuid) {
      if (chat?.status === 'CLOSED') {
        try {
          await updateChatStatusMutation.mutateAsync({ uuid: uuid, status: 'OPEN' });
          queryClient.invalidateQueries({ queryKey: ['org-chat', uuid] });
        } catch (error) {
          console.error('Error reopening chat:', error);
          return;
        }
      }

      const messageToSend = newMessage.trim();

      const optimisticMessage: LiveChatMessage = {
        uuid: crypto.randomUUID(), // Temporary client-side UUID
        // chat_uuid: uuid, // Removed, as it's not part of LiveChatMessage type
        content: messageToSend,
        created_at: new Date().toISOString(),
        sender_role: 'STAFF', // Changed from 'ORGANIZATION' to 'STAFF'
        is_system_message: false,
        is_read: false, // Added default for is_read
        sender: { // This should conform to LiveChatUser
          identifier: `org-staff-identifier-${crypto.randomUUID()}`, // Placeholder identifier
          first_name: 'Staff', // Placeholder for current organization user
          last_name: 'User',
          email: '<EMAIL>', // Placeholder email
          // image is optional
        } as LiveChatUser,
        // Ensure all required fields of LiveChatMessage are present
        // read_by_patient_at: null, // if needed and part of type
        // read_by_org_at: null, // if needed and part of type
      };

      // Optimistically update the UI
      queryClient.setQueryData<LiveChatMessage[]>(['org-chat-messages', uuid], (oldMessages = []) => [
        ...oldMessages,
        optimisticMessage,
      ]);

      setNewMessage(''); // Clear input field immediately

      sendMessageMutation.mutate(
        { chatUuid: uuid, messageData: { content: messageToSend } },
        {
          onSuccess: (actualMessageFromServer) => {
            // Replace optimistic message with actual message from server
            queryClient.setQueryData<LiveChatMessage[]>(['org-chat-messages', uuid], (oldMessages = []) => {
              const updatedMessages = oldMessages.filter(msg => msg.uuid !== optimisticMessage.uuid);
              // Add actual message, ensuring no duplicates if it somehow got there (e.g. via WebSocket very fast)
              if (!updatedMessages.some(msg => msg.uuid === actualMessageFromServer.uuid)) {
                updatedMessages.push(actualMessageFromServer);
              }
              return updatedMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
            });
          },
          onError: (error) => {
            console.error('Error sending message:', error);
            // Rollback: remove the optimistic message
            queryClient.setQueryData<LiveChatMessage[]>(['org-chat-messages', uuid], (oldMessages = []) =>
              oldMessages.filter(msg => msg.uuid !== optimisticMessage.uuid)
            );
            // Restore the message to the input field for the user to retry or edit
            setNewMessage(messageToSend);
            // Optionally, display an error message to the user
          },
        }
      );
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const handleStatusChange = (newStatus: 'OPEN' | 'CLOSED') => {
    // Use the new mutation to update status
    updateChatStatusMutation.mutate({ uuid: uuid as string, status: newStatus });
    setIsStatusDropdownOpen(false);
  };

  if (!chat) {
    return <div>Loading...</div>;
  }

  return (
    <div className="org-livechat-container">
      <div className="org-livechat-chat-header">
        <button
          className="org-livechat-back-button"
          onClick={() => navigate('/org/dashboard/live-chat')}
        >
          <ArrowLeft size={24} />
        </button>
        <div className="org-livechat-chat-info">
          <h2>{chat.subject}</h2>
          <div className="org-livechat-chat-meta">
            <span className="org-livechat-patient-name">
              {chat.patient.first_name} {chat.patient.last_name}
            </span>
            <span className="org-livechat-department-name">
              {chat.department.name}
            </span>
            <span className={`org-livechat-chat-status ${chat.status.toLowerCase()}`}>
              {chat.status}
            </span>
          </div>
        </div>
        {chat.status === 'OPEN' ? (
          <div className="org-livechat-actions">
            <button className="org-livechat-options-btn" onClick={() => {
              setIsStatusDropdownOpen(!isStatusDropdownOpen);
            }}>
              ...
            </button>
            {isStatusDropdownOpen && (
              <div className="org-livechat-status-dropdown">
                {/* Show Close if chat is OPEN */}
                {chat.status === 'OPEN' && <div onClick={() => {
                  handleStatusChange('CLOSED');
                }}>Close</div>}
                {/* "Reopen" option removed as it's unreachable when chat.status is 'OPEN'. */}
                {/* To implement "Reopen", place it where chat.status is 'CLOSED'. */}
                {/* Handle Pending status if needed */}
              </div>
            )}
          </div>
        ) : (
          <button className="org-livechat-call-btn">
            <Phone size={18} />
            Start Call
          </button>
        )}
      </div>

      <div className="nurtify-org-livechat-messages-container">
        {messages.map((message) => {
          const messageType = message.is_system_message
            ? 'system'
            : message.sender_role
              ? message.sender_role.toLowerCase() === 'patient'
                ? 'patient'
                : 'organization'
              : '';

          if (message.is_system_message) {
            return (
              <div
                key={message.uuid}
                className={`nurtify-org-livechat-message ${messageType}`}
              >
                <div className="nurtify-org-message-bubble">
                  <div className="message-content">{message.content}</div>
                </div>
              </div>
            );
          }

          return (
            <div
              key={message.uuid}
              className={`nurtify-org-livechat-message ${messageType}`}
            >
              {messageType === 'patient' && (
                <div className="nurtify-org-message-avatar">
                  <div className="avatar-circle">
                    {chat.patient.first_name.charAt(0)}
                  </div>
                </div>
              )}

              <div className="nurtify-org-message-content">
                <div className="org-livechat-message-header">
                  <span className="sender-name">{message.sender?.first_name} {message.sender?.last_name}</span>
                </div>
                <div className="nurtify-org-message-bubble">
                  <div className="message-content">{message.content}</div>
                </div>
                <div className="org-livechat-message-meta">
                  <span className="message-time">{formatTime(message.created_at)}</span>
                </div>
              </div>

              {messageType === 'organization' && (
                <div className="nurtify-org-message-avatar">
                  <div className="avatar-circle">
                    {message.sender?.first_name?.charAt(0) || 'O'}
                  </div>
                </div>
              )}
            </div>
          );
        })}
        <div ref={messagesEndRef} />
      </div>

      <form className="org-livechat-message-input" onSubmit={handleSendMessage}>
        <input
          type="text"
          placeholder="Type a message..."
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onMouseEnter={() => {
            if (uuid) {
              markChatMessagesAsRead.mutateAsync(uuid).catch((error) => {
                console.error('Error marking messages as read on mouse enter:', error);
              });
              console.log('Mouse entered');
            }
          }}
          disabled={sendMessageMutation.isPending}
        />

        <button
          type="submit"
          disabled={!newMessage.trim() || sendMessageMutation.isPending}

          className="send-button"
        >

          <Send size={18} />

        </button>
      </form>
    </div>
  );
};

export default OrgChatDetail;
