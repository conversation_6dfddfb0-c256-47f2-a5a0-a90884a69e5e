import React, { useState, useEffect, useRef } from 'react';
import { useKeycloak } from '@react-keycloak/web';
import { createNotificationWebSocketUrl, createLiveChatWebSocketUrl, createWebSocketConnection } from '@/utils/websocket';

interface WebSocketStatus {
  notifications: 'disconnected' | 'connecting' | 'connected' | 'error';
  liveChat: 'disconnected' | 'connecting' | 'connected' | 'error';
}

const WebSocketTester: React.FC = () => {
  const [status, setStatus] = useState<WebSocketStatus>({
    notifications: 'disconnected',
    liveChat: 'disconnected'
  });
  const [messages, setMessages] = useState<string[]>([]);
  const notificationWsRef = useRef<WebSocket | null>(null);
  const liveChatWsRef = useRef<WebSocket | null>(null);
  const { keycloak } = useKeycloak();

  const addMessage = (message: string) => {
    setMessages(prev => [`${new Date().toLocaleTimeString()}: ${message}`, ...prev.slice(0, 9)]);
  };

  const testNotificationWebSocket = () => {
    if (notificationWsRef.current) {
      notificationWsRef.current.close();
    }

    setStatus(prev => ({ ...prev, notifications: 'connecting' }));
    addMessage('Connecting to notification WebSocket...');

    // Check if user is authenticated and get Keycloak token
    if (!keycloak.authenticated || !keycloak.token) {
      addMessage('ERROR: User not authenticated or no Keycloak token available');
      setStatus(prev => ({ ...prev, notifications: 'error' }));
      return;
    }

    const wsUrl = createNotificationWebSocketUrl(keycloak.token);
    addMessage(`Notification WebSocket URL: ${wsUrl}`);

    const ws = createWebSocketConnection(
      wsUrl,
      // onMessage
      (event) => {
        addMessage(`Notification message received: ${event.data}`);
      },
      // onOpen
      () => {
        setStatus(prev => ({ ...prev, notifications: 'connected' }));
        addMessage('Notification WebSocket connected successfully!');
      },
      // onClose
      (event) => {
        setStatus(prev => ({ ...prev, notifications: 'disconnected' }));
        addMessage(`Notification WebSocket closed: ${event.code} - ${event.reason}`);
      },
      // onError
      (error) => {
        setStatus(prev => ({ ...prev, notifications: 'error' }));
        addMessage(`Notification WebSocket error: ${error}`);
      }
    );

    notificationWsRef.current = ws;
  };

  const testLiveChatWebSocket = () => {
    if (liveChatWsRef.current) {
      liveChatWsRef.current.close();
    }

    setStatus(prev => ({ ...prev, liveChat: 'connecting' }));
    addMessage('Connecting to live chat WebSocket...');

    // Use a test chat ID
    const testChatId = 'test-chat-id';
    const wsUrl = createLiveChatWebSocketUrl(testChatId);
    addMessage(`Live Chat WebSocket URL: ${wsUrl}`);

    const ws = createWebSocketConnection(
      wsUrl,
      // onMessage
      (event) => {
        addMessage(`Live chat message received: ${event.data}`);
      },
      // onOpen
      () => {
        setStatus(prev => ({ ...prev, liveChat: 'connected' }));
        addMessage('Live Chat WebSocket connected successfully!');
      },
      // onClose
      (event) => {
        setStatus(prev => ({ ...prev, liveChat: 'disconnected' }));
        addMessage(`Live Chat WebSocket closed: ${event.code} - ${event.reason}`);
      },
      // onError
      (error) => {
        setStatus(prev => ({ ...prev, liveChat: 'error' }));
        addMessage(`Live Chat WebSocket error: ${error}`);
      }
    );

    liveChatWsRef.current = ws;
  };

  const disconnectAll = () => {
    if (notificationWsRef.current) {
      notificationWsRef.current.close();
      notificationWsRef.current = null;
    }
    if (liveChatWsRef.current) {
      liveChatWsRef.current.close();
      liveChatWsRef.current = null;
    }
    addMessage('All WebSocket connections closed');
  };

  const clearMessages = () => {
    setMessages([]);
  };

  useEffect(() => {
    // Cleanup on unmount
    return () => {
      disconnectAll();
    };
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return '#22c55e';
      case 'connecting': return '#f59e0b';
      case 'error': return '#ef4444';
      default: return '#6b7280';
    }
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: 'white', 
      border: '1px solid #ccc', 
      borderRadius: '8px', 
      padding: '16px', 
      maxWidth: '400px',
      zIndex: 9999,
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
    }}>
      <h3 style={{ margin: '0 0 16px 0', fontSize: '16px' }}>WebSocket Tester</h3>
      
      <div style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
          <span style={{ 
            width: '12px', 
            height: '12px', 
            borderRadius: '50%', 
            backgroundColor: getStatusColor(status.notifications) 
          }}></span>
          <span>Notifications: {status.notifications}</span>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ 
            width: '12px', 
            height: '12px', 
            borderRadius: '50%', 
            backgroundColor: getStatusColor(status.liveChat) 
          }}></span>
          <span>Live Chat: {status.liveChat}</span>
        </div>
      </div>

      <div style={{ display: 'flex', gap: '8px', marginBottom: '16px', flexWrap: 'wrap' }}>
        <button 
          onClick={testNotificationWebSocket}
          style={{ padding: '4px 8px', fontSize: '12px', border: '1px solid #ccc', borderRadius: '4px', background: '#f8f9fa' }}
        >
          Test Notifications
        </button>
        <button 
          onClick={testLiveChatWebSocket}
          style={{ padding: '4px 8px', fontSize: '12px', border: '1px solid #ccc', borderRadius: '4px', background: '#f8f9fa' }}
        >
          Test Live Chat
        </button>
        <button 
          onClick={disconnectAll}
          style={{ padding: '4px 8px', fontSize: '12px', border: '1px solid #ccc', borderRadius: '4px', background: '#fee2e2' }}
        >
          Disconnect All
        </button>
        <button 
          onClick={clearMessages}
          style={{ padding: '4px 8px', fontSize: '12px', border: '1px solid #ccc', borderRadius: '4px', background: '#f0f9ff' }}
        >
          Clear Log
        </button>
      </div>

      <div style={{ 
        maxHeight: '200px', 
        overflowY: 'auto', 
        border: '1px solid #e5e7eb', 
        borderRadius: '4px', 
        padding: '8px',
        fontSize: '12px',
        fontFamily: 'monospace',
        backgroundColor: '#f9fafb'
      }}>
        {messages.length === 0 ? (
          <div style={{ color: '#6b7280' }}>No messages yet...</div>
        ) : (
          messages.map((message, index) => (
            <div key={index} style={{ marginBottom: '4px' }}>
              {message}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default WebSocketTester;
