import { useState, useEffect, useRef, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useKeycloak } from '@react-keycloak/web';
import { createNotificationWebSocketUrl, createWebSocketConnection } from '@/utils/websocket';
import { markNotificationReceived, markNotificationProcessed } from '@/utils/notificationPerformance';

interface UseRealTimeNotificationsOptions {
  queryKey: string[];
  onNewNotification?: (notification: any) => void;
  reconnectDelay?: number;
  maxReconnectAttempts?: number;
}

export const useRealTimeNotifications = ({
  queryKey,
  onNewNotification,
  reconnectDelay = 3000,
  maxReconnectAttempts = 5
}: UseRealTimeNotificationsOptions) => {
  const [isConnected, setIsConnected] = useState(false);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const queryClient = useQueryClient();
  const { keycloak } = useKeycloak();

  const invalidateQueries = useCallback(() => {
    // Immediately invalidate all related queries
    queryClient.invalidateQueries({ queryKey });
    queryClient.invalidateQueries({ queryKey: [...queryKey, 'unread-count'] });
    
    // Force immediate refetch
    queryClient.refetchQueries({ queryKey });
    queryClient.refetchQueries({ queryKey: [...queryKey, 'unread-count'] });
  }, [queryClient, queryKey]);

  const connectWebSocket = useCallback(() => {
    // Don't attempt to connect if we've exceeded max attempts
    if (reconnectAttempts >= maxReconnectAttempts) {
      console.warn(`Max reconnection attempts (${maxReconnectAttempts}) reached for notifications`);
      return;
    }

    // Close existing connection if any
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.close();
    }

    if (!keycloak.authenticated || !keycloak.token) {
      console.warn('Cannot connect WebSocket: User not authenticated');
      return;
    }

    console.log(`Connecting notification WebSocket (attempt ${reconnectAttempts + 1}/${maxReconnectAttempts})`);
    const wsUrl = createNotificationWebSocketUrl(keycloak.token);

    const ws = createWebSocketConnection(
      wsUrl,
      // onMessage
      (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('Real-time notification received:', data);

          if (data.type === 'notification') {
            const notificationId = data.notification?.uuid || `notification-${Date.now()}`;

            // Mark as received for performance monitoring
            markNotificationReceived(notificationId, 'websocket');

            // Call custom handler if provided
            onNewNotification?.(data.notification);

            // Mark as processed
            markNotificationProcessed(notificationId);

            // Immediately invalidate and refetch queries
            invalidateQueries();
          } else if (data.type === 'unread_count_update') {
            // Force immediate count update
            queryClient.invalidateQueries({ queryKey: [...queryKey, 'unread-count'] });
            queryClient.refetchQueries({ queryKey: [...queryKey, 'unread-count'] });
          }
        } catch (error) {
          console.error('Error parsing notification WebSocket message:', error);
        }
      },
      // onOpen
      () => {
        console.log('Real-time notification WebSocket connected successfully');
        setIsConnected(true);
        setReconnectAttempts(0); // Reset attempts on successful connection
        
        // Clear any pending reconnection
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
      },
      // onClose
      (event) => {
        console.log('Real-time notification WebSocket closed:', event.code, event.reason);
        setIsConnected(false);
        
        // Only attempt to reconnect if it wasn't a normal closure and user is still authenticated
        if (event.code !== 1000 && keycloak.authenticated && keycloak.token && reconnectAttempts < maxReconnectAttempts) {
          console.log(`Scheduling notification WebSocket reconnection in ${reconnectDelay}ms...`);
          reconnectTimeoutRef.current = setTimeout(() => {
            setReconnectAttempts(prev => prev + 1);
            connectWebSocket();
          }, reconnectDelay);
        }
      },
      // onError
      (error) => {
        console.error('Real-time notification WebSocket error:', error);
        setIsConnected(false);
      }
    );

    wsRef.current = ws;
  }, [keycloak.authenticated, keycloak.token, reconnectAttempts, maxReconnectAttempts, reconnectDelay, onNewNotification, invalidateQueries, queryClient, queryKey]);

  useEffect(() => {
    // Clear any existing reconnection timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Check if Keycloak is initialized and user is authenticated
    if (!keycloak.didInitialize) {
      console.log('Keycloak not yet initialized for real-time notifications, waiting...');
      return;
    }

    if (!keycloak.authenticated || !keycloak.token) {
      console.warn('User not authenticated for real-time notifications');
      setIsConnected(false);
      return;
    }

    connectWebSocket();

    return () => {
      // Clear reconnection timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
      
      // Close WebSocket connection
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.close();
      }
      
      setIsConnected(false);
    };
  }, [connectWebSocket, keycloak.didInitialize, keycloak.authenticated, keycloak.token]);

  // Manual reconnect function
  const reconnect = useCallback(() => {
    setReconnectAttempts(0);
    connectWebSocket();
  }, [connectWebSocket]);

  return {
    isConnected,
    reconnectAttempts,
    reconnect,
    maxReconnectAttempts
  };
};
