import { useState, useEffect, useRef } from 'react';
import { Bell } from 'lucide-react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useKeycloak } from '@react-keycloak/web';
import {
  getUnreadCount,
  getRecentNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  SystemNotification
} from '@/services/api/notification.service';
import { useRealTimeNotifications } from '@/hooks/useRealTimeNotifications';
import { markNotificationDisplayed } from '@/utils/notificationPerformance';

const SystemNotificationBell = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<SystemNotification[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();
  const { keycloak } = useKeycloak();

  // Use the real-time notifications hook
  const { isConnected } = useRealTimeNotifications({
    queryKey: ['system-notifications'],
    onNewNotification: (notification: SystemNotification) => {
      console.log('New system notification received via hook:', notification);
      // Immediately add to local state for instant UI update
      setNotifications(prev => {
        // Avoid duplicates by checking if notification already exists
        const exists = prev.some(n => n.uuid === notification.uuid);
        if (exists) return prev;
        return [notification, ...prev.slice(0, 9)]; // Keep only 10 most recent
      });

      // Mark as displayed for performance monitoring
      markNotificationDisplayed(notification.uuid);
    }
  });

  // Fetch unread count
  const { data: unreadCount = 0 } = useQuery({
    queryKey: ['system-notifications', 'unread-count'],
    queryFn: async () => {
      try {
        const response = await getUnreadCount();
        return response.count ?? 0;
      } catch (error) {
        console.error('Error fetching unread count:', error);
        return 0;
      }
    },
    staleTime: 0, // Always consider stale for real-time updates
    refetchInterval: 2000, // Reduced to 2 seconds for faster updates
    refetchIntervalInBackground: true, // Continue refetching in background
  });

  // Fetch recent notifications
  const { data: notificationsData } = useQuery({
    queryKey: ['system-notifications'],
    queryFn: async () => {
      const response = await getRecentNotifications();
      return response;
    },
    staleTime: 0, // Always consider stale for real-time updates
    refetchInterval: 2000, // Reduced to 2 seconds for faster updates
    refetchIntervalInBackground: true, // Continue refetching in background
  });

  useEffect(() => {
    if (notificationsData) {
      setNotifications(notificationsData);
      // Mark notifications as displayed for performance monitoring
      notificationsData.forEach((notification: SystemNotification) => {
        markNotificationDisplayed(notification.uuid);
      });
    }
  }, [notificationsData]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);



  const handleNotificationClick = async (notification: SystemNotification) => {
    if (!notification.read_at) {
      try {
        // Mark as read
        await markNotificationAsRead(notification.uuid);
        
        // Update local state
        setNotifications(prev => 
          prev.map(n => 
            n.uuid === notification.uuid 
              ? { ...n, read_at: new Date().toISOString() }
              : n
          )
        );

        // Refetch unread count
        queryClient.invalidateQueries({ queryKey: ['system-notifications', 'unread-count'] });
      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
    }
  };

  const handleMarkAllRead = async () => {
    try {
      await markAllNotificationsAsRead();
      
      // Update local state
      setNotifications(prev => 
        prev.map(n => ({ ...n, read_at: new Date().toISOString() }))
      );

      // Refetch unread count
      queryClient.invalidateQueries({ queryKey: ['system-notifications', 'unread-count'] });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    // Less than 1 minute
    if (diff < 60000) {
      return 'Just now';
    }
    // Less than 1 hour
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return `${minutes}m ago`;
    }
    // Less than 24 hours
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000);
      return `${hours}h ago`;
    }
    // More than 24 hours
    return date.toLocaleDateString();
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return '#ef4444';
      case 'normal':
        return '#37B7C3';
      case 'low':
        return '#6b7280';
      default:
        return '#37B7C3';
    }
  };

  return (
    <div className="system-notification-bell" ref={dropdownRef}>
      <div
        className="notification-icon-wrapper"
        onClick={() => setIsOpen(!isOpen)}
        tabIndex={0}
        role="button"
        aria-label="System notifications"
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') setIsOpen(!isOpen);
        }}
        style={{ 
          position: 'relative', 
          cursor: 'pointer', 
          margin: '0 10px',
          padding: '8px',
          borderRadius: '50%',
          transition: 'background-color 0.2s ease'
        }}
        onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'rgba(55, 183, 195, 0.1)'}
        onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
      >
        <Bell size={20} color="#37B7C3" />
        {unreadCount > 0 && (
          <span
            className="notification-badge"
            style={{
              position: 'absolute',
              top: '-2px',
              right: '-2px',
              backgroundColor: '#ef4444',
              color: 'white',
              borderRadius: '50%',
              width: '18px',
              height: '18px',
              fontSize: '11px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontWeight: 'bold',
              border: '2px solid white'
            }}
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
        {/* Connection status indicator */}
        <div
          style={{
            position: 'absolute',
            bottom: '-2px',
            left: '-2px',
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: isConnected ? '#10b981' : '#ef4444',
            border: '1px solid white',
            opacity: 0.8
          }}
          title={isConnected ? 'Real-time notifications connected' : 'Real-time notifications disconnected'}
        />
      </div>

      {isOpen && (
        <div 
          className="notification-dropdown"
          style={{
            position: 'absolute',
            top: '100%',
            right: '0',
            width: '380px',
            maxHeight: '500px',
            backgroundColor: 'white',
            borderRadius: '12px',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',
            border: '1px solid #e5e7eb',
            zIndex: 1000,
            overflow: 'hidden',
            marginTop: '8px'
          }}
        >
          <div 
            className="notification-dropdown-header"
            style={{
              padding: '16px 20px',
              borderBottom: '1px solid #f3f4f6',
              backgroundColor: '#f9fafb',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}
          >
            <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600', color: '#111827' }}>
              Notifications
            </h3>
            {notifications.length > 0 && (
              <button
                onClick={handleMarkAllRead}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#37B7C3',
                  fontSize: '12px',
                  cursor: 'pointer',
                  fontWeight: '500',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  transition: 'background-color 0.2s ease'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'rgba(55, 183, 195, 0.1)'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                Mark all read
              </button>
            )}
          </div>

          <div 
            className="notification-list"
            style={{
              maxHeight: '400px',
              overflowY: 'auto'
            }}
          >
            {notifications.length === 0 ? (
              <div 
                className="no-notifications"
                style={{
                  padding: '40px 20px',
                  textAlign: 'center',
                  color: '#6b7280',
                  fontSize: '14px'
                }}
              >
                No notifications
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.uuid}
                  className={`notification-item ${!notification.read_at ? 'unread' : ''}`}
                  onClick={() => handleNotificationClick(notification)}
                  style={{
                    padding: '16px 20px',
                    borderBottom: '1px solid #f3f4f6',
                    cursor: 'pointer',
                    transition: 'background-color 0.2s ease',
                    backgroundColor: notification.read_at ? 'white' : '#fef7f0',
                    position: 'relative'
                  }}
                  onMouseOver={(e) => e.currentTarget.style.backgroundColor = notification.read_at ? '#f9fafb' : '#fef3e2'}
                  onMouseOut={(e) => e.currentTarget.style.backgroundColor = notification.read_at ? 'white' : '#fef7f0'}
                >
                  {!notification.read_at && (
                    <div
                      style={{
                        position: 'absolute',
                        left: '8px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        width: '6px',
                        height: '6px',
                        borderRadius: '50%',
                        backgroundColor: getPriorityColor(notification.priority)
                      }}
                    />
                  )}
                  <div className="notification-content">
                    <div 
                      className="notification-title"
                      style={{
                        fontWeight: notification.read_at ? '500' : '600',
                        fontSize: '14px',
                        color: '#111827',
                        marginBottom: '4px',
                        lineHeight: '1.4'
                      }}
                    >
                      {notification.title}
                    </div>
                    <div 
                      className="notification-message"
                      style={{
                        fontSize: '13px',
                        color: '#6b7280',
                        marginBottom: '8px',
                        lineHeight: '1.4',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                      }}
                    >
                      {notification.message}
                    </div>
                    <div 
                      className="notification-meta"
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        fontSize: '11px',
                        color: '#9ca3af'
                      }}
                    >
                      <span 
                        className="notification-type"
                        style={{
                          backgroundColor: getPriorityColor(notification.priority) + '20',
                          color: getPriorityColor(notification.priority),
                          padding: '2px 6px',
                          borderRadius: '4px',
                          fontWeight: '500',
                          textTransform: 'capitalize'
                        }}
                      >
                        {notification.notification_type_name.replace(/_/g, ' ')}
                      </span>
                      <span className="notification-time">
                        {formatTime(notification.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemNotificationBell;
