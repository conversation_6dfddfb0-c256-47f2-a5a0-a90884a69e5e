import { useState, useEffect, useRef } from 'react';
import { MessageSquare } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useKeycloak } from '@react-keycloak/web';
import api from '@/services/api'; // Use your custom api instance
import { createNotificationWebSocketUrl, createWebSocketConnection } from '@/utils/websocket';

interface Notification {
  uuid: string;
  chat_uuid: string;
  chat_subject: string;
  message: {
    content: string;
    sender: {
      name: string;
      role: string;
    };
    created_at: string;
  };
  is_read: boolean;
}

const NotificationBell = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { keycloak } = useKeycloak();

  // Fetch unread count
  const { data: unreadCount = 0 } = useQuery({
    queryKey: ['notifications', 'unread-count'],
    queryFn: async () => {
      try {
        const response = await api.get('/live-chat/notifications/unread_count/'); // Use api and correct path
        return response.data.count ?? 0;
      } catch (error) {
        console.error('Error fetching unread count:', error);
        return 0;
      }
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Fetch notifications
  const { data: notificationsData } = useQuery({
    queryKey: ['notifications'],
    queryFn: async () => {
      const response = await api.get('/live-chat/notifications/'); // Use api and correct path
      return response.data;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  useEffect(() => {
    if (notificationsData) {
      setNotifications(notificationsData);
    }
  }, [notificationsData]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // WebSocket connection for real-time notifications
  useEffect(() => {
    // Check if Keycloak is initialized and user is authenticated
    if (!keycloak.didInitialize) {
      console.log('Keycloak not yet initialized, waiting...');
      return;
    }

    if (!keycloak.authenticated || !keycloak.token) {
      console.warn('User not authenticated or no Keycloak token available for notification WebSocket', {
        authenticated: keycloak.authenticated,
        hasToken: !!keycloak.token,
        initialized: keycloak.didInitialize
      });
      return;
    }

    console.log('Connecting notification WebSocket with Keycloak token');
    const wsUrl = createNotificationWebSocketUrl(keycloak.token);

    const ws = createWebSocketConnection(
      wsUrl,
      // onMessage
      (event) => {
        try {
          const data = JSON.parse(event.data);
          if (data.type === 'notification') {
            // Add new notification to the list
            setNotifications(prev => [data.notification, ...prev]);
            // Refetch unread count
            queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });
          }
        } catch (error) {
          console.error('Error parsing notification WebSocket message:', error);
        }
      },
      // onOpen
      () => {
        console.log('Notification WebSocket connection established');
      },
      // onClose
      (event) => {
        // Only attempt to reconnect if it wasn't a normal closure
        if (event.code !== 1000) {
          setTimeout(() => {
            console.log('Attempting to reconnect notification WebSocket...');
            // Reconnection will happen when the effect re-runs
          }, 5000);
        }
      },
      // onError
      (error) => {
        console.error('Notification WebSocket error:', error);
      }
    );

    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [queryClient, keycloak.authenticated, keycloak.token, keycloak.didInitialize]);

  const handleNotificationClick = async (notification: Notification) => {
    // Mark as read
    await api.post(`/live-chat/notifications/${notification.uuid}/mark_as_read/`); // Use api and correct path

    // Navigate to chat
    navigate(`/org/dashboard/live-chat/${notification.chat_uuid}`);

    // Close dropdown
    setIsOpen(false);

    // Refetch notifications and unread count
    queryClient.invalidateQueries({ queryKey: ['notifications'] });
    queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    // Less than 1 hour
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return `${minutes}m ago`;
    }
    // Less than 24 hours
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000);
      return `${hours}h ago`;
    }
    // More than 24 hours
    return date.toLocaleDateString();
  };

  return (
    <div className="notification-bell" ref={dropdownRef}>
      <div
        className="menu-icon-wrapper"
        onClick={() => navigate('/org/dashboard/live-chat')}
        tabIndex={0}
        role="button"
        aria-label="Go to chat"
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') navigate('/org/dashboard/live-chat');
        }}
        style={{ position: 'relative', cursor: 'pointer', margin: '0 10px' }}
      >
        <MessageSquare size={24} />
        {unreadCount > 0 && (
          <span className="notification-badge">{unreadCount}</span>
        )}
      </div>

      {isOpen && (
        <div className="notification-dropdown">
          <div className="notification-dropdown-header" style={{ fontWeight: 600, fontSize: '16px', padding: '12px 16px', borderBottom: '1px solid #f0f0f0', background: '#fff' }}>
            Messages
          </div>
          <div className="notification-header">
            <h3>Notifications</h3>
            {notifications.length > 0 && (
              <button
                className="mark-all-read"
                onClick={async () => {
                  await api.post('/live-chat/notifications/mark_all_as_read/'); // Use api and correct path
                  queryClient.invalidateQueries({ queryKey: ['notifications'] });
                  queryClient.invalidateQueries({ queryKey: ['notifications', 'unread-count'] });
                }}
              >
                Mark all as read
              </button>
            )}
          </div>

          <div className="notification-list">
            {notifications.length === 0 ? (
              <div className="no-notifications">
                No notifications
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.uuid}
                  className={`notification-item ${!notification.is_read ? 'unread' : ''}`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="notification-content">
                    <div className="notification-title">
                      {notification.chat_subject}
                    </div>
                    <div className="notification-message">
                      {notification.message.content}
                    </div>
                    <div className="notification-meta">
                      <span className="notification-sender">
                        {notification.message.sender.name}
                      </span>
                      <span className="notification-time">
                        {formatTime(notification.message.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationBell;
