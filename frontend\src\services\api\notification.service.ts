import api from '@/services/api';

export interface SystemNotification {
  uuid: string;
  title: string;
  message: string;
  notification_type: {
    id: number;
    name: string;
    description: string;
  };
  notification_type_name: string;
  priority: 'low' | 'normal' | 'high';
  status: string;
  data: Record<string, any>;
  created_at: string;
  sent_at: string;
  read_at: string | null;
  send_push: boolean;
  send_email: boolean;
  send_sms: boolean;
  send_in_app: boolean;
}

export interface NotificationType {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface NotificationPreferences {
  id: number;
  push_enabled: boolean;
  email_enabled: boolean;
  sms_enabled: boolean;
  in_app_enabled: boolean;
  quiet_hours_start: string;
  quiet_hours_end: string;
  type_preferences: Record<string, {
    push: boolean;
    email: boolean;
    sms: boolean;
    in_app: boolean;
  }>;
  created_at: string;
  updated_at: string;
}

export interface PushToken {
  id: number;
  token: string;
  platform: 'web' | 'ios' | 'android';
  device_id: string;
  is_active: boolean;
  created_at: string;
  last_used: string;
}

export interface NotificationTemplate {
  id: number;
  name: string;
  notification_type: {
    id: number;
    name: string;
    description: string;
  };
  title_template: string;
  message_template: string;
  email_subject_template: string;
  email_body_template: string;
  sms_template: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface PaginatedNotifications {
  count: number;
  next: string | null;
  previous: string | null;
  results: SystemNotification[];
}

export interface UnreadCountResponse {
  count: number;
}

export interface MarkReadResponse {
  status: string;
}

export interface MarkAllReadResponse {
  updated_count: number;
}

// User Notifications
export const getNotifications = async (page: number = 1, limit: number = 20): Promise<PaginatedNotifications> => {
  const response = await api.get(`/notification/notifications/?page=${page}&limit=${limit}`);
  return response.data;
};

export const getRecentNotifications = async (): Promise<SystemNotification[]> => {
  const response = await api.get('/notification/notifications/recent/');
  return response.data;
};

export const getUnreadCount = async (): Promise<UnreadCountResponse> => {
  const response = await api.get('/notification/notifications/unread_count/');
  return response.data;
};

export const getNotificationsByType = async (type: string): Promise<SystemNotification[]> => {
  const response = await api.get(`/notification/notifications/by_type/?type=${type}`);
  return response.data;
};

export const markNotificationAsRead = async (uuid: string): Promise<MarkReadResponse> => {
  const response = await api.post(`/notification/notifications/${uuid}/mark_read/`);
  return response.data;
};

export const markAllNotificationsAsRead = async (): Promise<MarkAllReadResponse> => {
  const response = await api.post('/notification/notifications/mark_all_read/');
  return response.data;
};

// Notification Types
export const getNotificationTypes = async (): Promise<NotificationType[]> => {
  const response = await api.get('/notification/types/');
  return response.data;
};

// User Preferences
export const getNotificationPreferences = async (): Promise<NotificationPreferences> => {
  const response = await api.get('/notification/preferences/');
  return response.data;
};

export const updateNotificationPreferences = async (preferences: Partial<NotificationPreferences>): Promise<NotificationPreferences> => {
  const response = await api.put('/notification/preferences/', preferences);
  return response.data;
};

export const updateSpecificPreferences = async (preferences: Partial<NotificationPreferences>): Promise<NotificationPreferences> => {
  const response = await api.post('/notification/preferences/update_preferences/', preferences);
  return response.data;
};

// Push Notification Tokens
export const getPushTokens = async (): Promise<PushToken[]> => {
  const response = await api.get('/notification/push-tokens/');
  return response.data;
};

export const registerDeviceForPush = async (token: string, platform: string, deviceId: string): Promise<PushToken> => {
  const response = await api.post('/notification/push-tokens/register_device/', {
    token,
    platform,
    device_id: deviceId
  });
  return response.data;
};

export const unregisterDevice = async (deviceId?: string, token?: string): Promise<void> => {
  const payload = deviceId ? { device_id: deviceId } : { token };
  await api.post('/notification/push-tokens/unregister_device/', payload);
};

// Notification Templates
export const getNotificationTemplates = async (): Promise<NotificationTemplate[]> => {
  const response = await api.get('/notification/templates/');
  return response.data;
};

// Admin Endpoints (Admin Users Only)
export const sendNotificationToUser = async (payload: {
  recipient_id: number;
  notification_type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high';
  send_push?: boolean;
  send_email?: boolean;
  send_sms?: boolean;
  send_in_app?: boolean;
}): Promise<SystemNotification> => {
  const response = await api.post('/notification/admin/send_notification/', payload);
  return response.data;
};

export const sendBatchNotifications = async (payload: {
  recipient_ids: number[];
  notification_type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high';
  batch_name?: string;
  send_push?: boolean;
  send_email?: boolean;
  send_sms?: boolean;
  send_in_app?: boolean;
}): Promise<{ batch_id: string; sent_count: number }> => {
  const response = await api.post('/notification/admin/send_batch_notification/', payload);
  return response.data;
};

// WebSocket helper functions
export const createNotificationWebSocket = (token: string): WebSocket => {
  const socketBaseUrl = import.meta.env.VITE_SOCKET_BASE_URL || 'ws://localhost:8003';
  const socketUrl = `${socketBaseUrl}/socket/notifications/?token=${token}`;
  return new WebSocket(socketUrl);
};

export const sendWebSocketMessage = (socket: WebSocket, message: {
  type: 'mark_read' | 'mark_all_read' | 'get_notifications' | 'update_preferences';
  notification_uuid?: string;
  page?: number;
  limit?: number;
  preferences?: Partial<NotificationPreferences>;
}): void => {
  if (socket.readyState === WebSocket.OPEN) {
    socket.send(JSON.stringify(message));
  }
};

