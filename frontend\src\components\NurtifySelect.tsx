import { useState, useEffect, ChangeEvent } from "react";
import "./styles.css";

interface Option {
    value: string;
    label: string;
    disabled?: boolean;
}

interface NurtifySelectProps {
    label?: string;
    name?: string;
    value?: string;
    onChange?: (e: ChangeEvent<HTMLSelectElement>) => void;
    options: Option[];
    disabled?: boolean; 
    placeholder?: string; 
    className?: string; 
    required?: boolean; 
}

const NurtifySelect: React.FC<NurtifySelectProps> = ({
    label,
    name,
    value,
    onChange,
    options,
    disabled = false,
    placeholder,
    className,
    required = false
}) => {
    // Ensure value is always controlled (never undefined)
    const controlledValue = value ?? '';
    const [selectedValue, setSelectedValue] = useState(controlledValue);

    // Update selectedValue when value prop changes
    useEffect(() => {
        setSelectedValue(controlledValue);
    }, [controlledValue]);

    const handleChange = (e: ChangeEvent<HTMLSelectElement>) => {
        setSelectedValue(e.target.value);
        onChange?.(e);
    };

    return (
        <div className={`nurtify-select ${className || ''}`}> 
            {label && <label htmlFor={name}>{label}</label>}
            <select
                style={{background:"none"}}
                id={name}
                name={name}
                value={selectedValue}
                onChange={handleChange}
                disabled={disabled} 
                required={required} 
                className="select-input form-control-sm"
            >
                {placeholder && ( 
                    <option value="" disabled>
                        {placeholder}
                    </option>
                )}
                {options.map((option) => (
                    <option 
                        key={option.value} 
                        value={option.value}
                        disabled={option.disabled}
                        style={option.disabled ? { color: '#6c757d', fontStyle: 'italic' } : {}}
                    >
                        {option.disabled ? `${option.label} (Already uploaded)` : option.label}
                    </option>
                ))}
            </select>
        </div>
    );
}

export default NurtifySelect;
